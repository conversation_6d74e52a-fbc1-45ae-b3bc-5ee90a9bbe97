<template>
  <div class="simple-header" :class="{ 'draggable': draggable }" :data-tauri-drag-region="draggable">
    <div class="header-content">
      <!-- 左侧标题 -->
      <div class="header-left">
        <slot name="left">
          <h3 class="title">{{ title }}</h3>
        </slot>
      </div>

      <!-- 中间可拖动区域 -->
      <div class="header-center" :data-tauri-drag-region="draggable">
        <slot name="center"></slot>
      </div>

      <!-- 右侧窗口控制 -->
      <div class="header-right">
        <slot name="right"></slot>
        
        <!-- 窗口控制按钮 -->
        <div class="window-controls" v-if="showWindowControls">
          <button
            class="control-btn minimize-btn"
            @click="handleMinimize"
            :title="minimizeTitle"
            :disabled="loading"
          >
            <MinusOutlined />
          </button>
          
          <button
            class="control-btn maximize-btn"
            @click="handleMaximize"
            :title="maximizeTitle"
            :disabled="loading"
          >
            <BorderOutlined v-if="!isMaximized" />
            <ShrinkOutlined v-else />
          </button>
          
          <button
            class="control-btn close-btn"
            @click="handleClose"
            :title="closeTitle"
            :disabled="loading"
          >
            <CloseOutlined />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { MinusOutlined, BorderOutlined, ShrinkOutlined, CloseOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  title: {
    type: String,
    default: '应用'
  },
  showWindowControls: {
    type: Boolean,
    default: true
  },
  draggable: {
    type: Boolean,
    default: true
  },
  minimizeTitle: {
    type: String,
    default: '最小化'
  },
  maximizeTitle: {
    type: String,
    default: '最大化/还原'
  },
  closeTitle: {
    type: String,
    default: '关闭'
  },
  useChildWindow: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['minimize', 'maximize', 'close', 'window-state-change'])

// State
const loading = ref(false)
const isMaximized = ref(false)
const currentWindow = ref(null)

// 检测是否在 Tauri 环境中
const isTauri = computed(() => {
  return typeof window !== 'undefined' && window.__TAURI__
})

// 初始化窗口管理
const initWindowManager = async () => {
  if (!isTauri.value) return

  try {
    const { getCurrentWebviewWindow } = await import('@tauri-apps/api/webviewWindow')
    currentWindow.value = getCurrentWebviewWindow()
    
    if (currentWindow.value) {
      // 检查当前窗口状态
      const isMaximizedState = await currentWindow.value.isMaximized()
      isMaximized.value = isMaximizedState
      
      // 监听窗口状态变化事件
      const unlisten = await currentWindow.value.listen('tauri://resize', async () => {
        const maximized = await currentWindow.value.isMaximized()
        isMaximized.value = maximized
        emit('window-state-change', { maximized })
      })
      
      return unlisten
    }
  } catch (error) {
    console.error('初始化窗口管理器失败:', error)
  }
}

// 窗口控制方法
const handleMinimize = async () => {
  if (loading.value) return

  loading.value = true
  try {
    emit('minimize')

    if (isTauri.value && currentWindow.value) {
      if (props.useChildWindow) {
        // 子窗口直接最小化
        await currentWindow.value.minimize()
      } else {
        // 主窗口使用自定义逻辑
        const { invoke } = await import('@tauri-apps/api/core')
        const windowLabel = currentWindow.value.label || 'main'
        await invoke('minimize_window_with_children', { windowId: windowLabel })
      }
    }
  } catch (error) {
    console.error('最小化窗口失败:', error)
  } finally {
    loading.value = false
  }
}

const handleMaximize = async () => {
  if (loading.value) return
  
  loading.value = true
  try {
    emit('maximize')
    
    if (isTauri.value && currentWindow.value) {
      if (isMaximized.value) {
        await currentWindow.value.unmaximize()
      } else {
        await currentWindow.value.maximize()
      }
      isMaximized.value = !isMaximized.value
    }
  } catch (error) {
    console.error('切换窗口最大化状态失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = async () => {
  if (loading.value) return

  loading.value = true
  try {
    emit('close')

    if (isTauri.value && currentWindow.value) {
      if (props.useChildWindow) {
        // 子窗口直接关闭
        await currentWindow.value.close()
      } else {
        // 主窗口使用自定义逻辑
        const { invoke } = await import('@tauri-apps/api/core')
        const windowLabel = currentWindow.value.label || 'main'
        await invoke('close_window_with_children', { windowId: windowLabel })
      }
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
let unlistenResize = null

onMounted(async () => {
  unlistenResize = await initWindowManager()
})

onUnmounted(() => {
  if (unlistenResize) {
    unlistenResize()
  }
})
</script>

<style scoped>
.simple-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0;
  height: 48px;
  position: relative;
  z-index: 1000;
}

.simple-header.draggable {
  -webkit-app-region: drag;
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  -webkit-app-region: no-drag;
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  -webkit-app-region: no-drag;
}

.title {
  margin: 0;
  color: #262626;
  font-weight: 500;
  font-size: 16px;
}

.window-controls {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 12px;
  padding: 4px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.02);
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.04);
  color: #595959;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.08);
  color: #262626;
  transform: translateY(-1px);
}

.control-btn:active {
  background: rgba(24, 144, 255, 0.1);
  transform: translateY(0);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .simple-header {
    background: #141414;
    border-bottom-color: #303030;
  }
  
  .title {
    color: #fff;
  }
  
  .control-btn {
    color: #8c8c8c;
  }
  
  .control-btn:hover {
    background: #262626;
    color: #fff;
  }
  
  .control-btn:active {
    background: #1890ff;
  }
}
</style>
